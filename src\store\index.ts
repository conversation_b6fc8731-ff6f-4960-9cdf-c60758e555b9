import { create } from 'zustand'
import { Conversation, Message, Settings, OpenRouterModel } from '../types'
import { openRouterService } from '../services/openrouter'

interface AppState {
  // Conversations
  conversations: Conversation[]
  currentConversationId: string | null
  messages: Message[]
  
  // Models
  models: OpenRouterModel[]
  
  // Settings
  settings: Settings
  
  // UI State
  sidebarOpen: boolean
  isLoading: boolean
  streamingMessage: string | null
  
  // Actions
  setConversations: (conversations: Conversation[]) => void
  setCurrentConversation: (id: string | null) => void
  setMessages: (messages: Message[]) => void
  addMessage: (message: Message) => void
  setModels: (models: OpenRouterModel[]) => void
  updateSettings: (settings: Partial<Settings>) => void
  setSidebarOpen: (open: boolean) => void
  setLoading: (loading: boolean) => void
  setStreamingMessage: (message: string | null) => void
  
  // Async actions
  loadConversations: () => Promise<void>
  loadMessages: (conversationId: string) => Promise<void>
  createConversation: (title: string) => Promise<string>
  deleteConversation: (id: string) => Promise<void>
  sendMessage: (content: string) => Promise<void>
  sendStreamingMessage: (content: string) => Promise<void>
  loadModels: () => Promise<void>
}

export const useAppStore = create<AppState>((set, get) => ({
  // Initial state
  conversations: [],
  currentConversationId: null,
  messages: [],
  models: [],
  settings: {
    temperature: 0.7,
    maxTokens: 4096,
    theme: 'dark',
    topP: 0.95,
    topK: 30,
    frequencyPenalty: 0,
    presencePenalty: 0,
    systemPrompt: '',
    favoriteModels: [],
    modelFilter: 'all',
  },
  sidebarOpen: true,
  isLoading: false,
  streamingMessage: null,
  
  // Sync actions
  setConversations: (conversations) => set({ conversations }),
  setCurrentConversation: (id) => set({ currentConversationId: id }),
  setMessages: (messages) => set({ messages }),
  addMessage: (message) => set((state) => ({ messages: [...state.messages, message] })),
  setModels: (models) => set({ models }),
  updateSettings: (newSettings) => set((state) => ({ 
    settings: { ...state.settings, ...newSettings } 
  })),
  setSidebarOpen: (open) => set({ sidebarOpen: open }),
  setLoading: (loading) => set({ isLoading: loading }),
  setStreamingMessage: (message) => set({ streamingMessage: message }),
  
  // Async actions
  loadConversations: async () => {
    try {
      const conversations = await window.electronAPI.db.getConversations()
      set({ conversations })
    } catch (error) {
      console.error('Failed to load conversations:', error)
    }
  },
  
  loadMessages: async (conversationId: string) => {
    try {
      const messages = await window.electronAPI.db.getMessages(conversationId)
      set({ messages })
    } catch (error) {
      console.error('Failed to load messages:', error)
    }
  },
  
  createConversation: async (title: string) => {
    try {
      console.log('Store: Creating conversation with title:', title)

      if (!window.electronAPI?.db) {
        throw new Error('Electron API not available')
      }

      const id = await window.electronAPI.db.createConversation(title)
      console.log('Store: Got conversation ID:', id)

      await get().loadConversations()
      console.log('Store: Reloaded conversations')

      return id
    } catch (error) {
      console.error('Failed to create conversation:', error)
      throw error
    }
  },
  
  deleteConversation: async (id: string) => {
    try {
      await window.electronAPI.db.deleteConversation(id)
      await get().loadConversations()
      if (get().currentConversationId === id) {
        set({ currentConversationId: null, messages: [] })
      }
    } catch (error) {
      console.error('Failed to delete conversation:', error)
    }
  },
  
  sendMessage: async (content: string) => {
    const { currentConversationId, settings, messages } = get()
    if (!currentConversationId) return

    try {
      set({ isLoading: true })

      // Add user message
      const userMessage: Omit<Message, 'id' | 'created_at'> = {
        conversation_id: currentConversationId,
        role: 'user',
        content,
      }

      await window.electronAPI.db.addMessage(currentConversationId, userMessage)
      await get().loadMessages(currentConversationId)

      // Check if API key is set
      if (!settings.openRouterApiKey) {
        const assistantMessage: Omit<Message, 'id' | 'created_at'> = {
          conversation_id: currentConversationId,
          role: 'assistant',
          content: 'Please set your OpenRouter API key in settings to start chatting with AI models.',
          model: 'system',
        }

        await window.electronAPI.db.addMessage(currentConversationId, assistantMessage)
        await get().loadMessages(currentConversationId)
        return
      }

      // Set API key and get response from OpenRouter
      openRouterService.setApiKey(settings.openRouterApiKey)

      // Prepare conversation history
      const conversationMessages = messages.map(msg => ({
        role: msg.role as 'user' | 'assistant' | 'system',
        content: msg.content,
      }))

      // Add system prompt if configured
      if (settings.systemPrompt && settings.systemPrompt.trim()) {
        conversationMessages.unshift({
          role: 'system' as const,
          content: settings.systemPrompt.trim(),
        })
      }

      // Add the new user message
      conversationMessages.push({
        role: 'user' as const,
        content,
      })

      try {
        const response = await openRouterService.createChatCompletion({
          model: settings.selectedModel || 'openai/gpt-3.5-turbo',
          messages: conversationMessages,
          temperature: settings.temperature,
          max_tokens: settings.maxTokens,
          top_p: settings.topP,
          top_k: settings.topK,
          frequency_penalty: settings.frequencyPenalty,
          presence_penalty: settings.presencePenalty,
        })

        const assistantMessage: Omit<Message, 'id' | 'created_at'> = {
          conversation_id: currentConversationId,
          role: 'assistant',
          content: response,
          model: settings.selectedModel,
        }

        await window.electronAPI.db.addMessage(currentConversationId, assistantMessage)
        await get().loadMessages(currentConversationId)

      } catch (apiError) {
        console.error('OpenRouter API error:', apiError)

        const errorMessage: Omit<Message, 'id' | 'created_at'> = {
          conversation_id: currentConversationId,
          role: 'assistant',
          content: `Error: ${apiError instanceof Error ? apiError.message : 'Failed to get response from AI model'}`,
          model: 'system',
        }

        await window.electronAPI.db.addMessage(currentConversationId, errorMessage)
        await get().loadMessages(currentConversationId)
      }

    } catch (error) {
      console.error('Failed to send message:', error)
    } finally {
      set({ isLoading: false })
    }
  },

  // Streaming message implementation
  sendStreamingMessage: async (content: string) => {
    const { currentConversationId, messages, settings } = get()

    if (!currentConversationId) {
      console.error('No conversation selected')
      return
    }

    set({ isLoading: true, streamingMessage: '' })

    try {
      // Add user message to database
      const userMessage: Omit<Message, 'id' | 'created_at'> = {
        conversation_id: currentConversationId,
        role: 'user',
        content,
        model: undefined,
      }

      await window.electronAPI.db.addMessage(currentConversationId, userMessage)
      await get().loadMessages(currentConversationId)

      // Check if API key is set
      if (!settings.openRouterApiKey) {
        const errorMessage: Omit<Message, 'id' | 'created_at'> = {
          conversation_id: currentConversationId,
          role: 'assistant',
          content: 'Please set your OpenRouter API key in settings to use AI models.',
          model: 'system',
        }

        await window.electronAPI.db.addMessage(currentConversationId, errorMessage)
        await get().loadMessages(currentConversationId)
        set({ isLoading: false, streamingMessage: null })
        return
      }

      // Check if model is selected
      if (!settings.selectedModel) {
        const errorMessage: Omit<Message, 'id' | 'created_at'> = {
          conversation_id: currentConversationId,
          role: 'assistant',
          content: 'Please select an AI model in the chat settings to continue.',
          model: 'system',
        }

        await window.electronAPI.db.addMessage(currentConversationId, errorMessage)
        await get().loadMessages(currentConversationId)
        set({ isLoading: false, streamingMessage: null })
        return
      }

      // Set API key and validate it
      openRouterService.setApiKey(settings.openRouterApiKey)

      // Validate API key before proceeding
      const validation = await openRouterService.validateApiKey()
      if (!validation.valid) {
        const errorMessage: Omit<Message, 'id' | 'created_at'> = {
          conversation_id: currentConversationId,
          role: 'assistant',
          content: `API Key Error: ${validation.error}`,
          model: 'system',
        }

        await window.electronAPI.db.addMessage(currentConversationId, errorMessage)
        await get().loadMessages(currentConversationId)
        set({ isLoading: false, streamingMessage: null })
        return
      }

      const conversationMessages = messages.map(msg => ({
        role: msg.role as 'user' | 'assistant' | 'system',
        content: msg.content,
      }))

      // Add system prompt if configured
      if (settings.systemPrompt && settings.systemPrompt.trim()) {
        conversationMessages.unshift({
          role: 'system' as const,
          content: settings.systemPrompt.trim(),
        })
      }

      // Add the new user message
      conversationMessages.push({
        role: 'user' as const,
        content,
      })

      let streamingContent = ''

      // Add timeout to prevent hanging
      const timeoutId = setTimeout(() => {
        console.error('Streaming request timed out after 60 seconds')
        const timeoutError = new Error('Request timed out. Please try again.')
        set({
          isLoading: false,
          streamingMessage: null
        })

        // Add timeout error message
        const errorMessage: Omit<Message, 'id' | 'created_at'> = {
          conversation_id: currentConversationId,
          role: 'assistant',
          content: 'Request timed out. Please check your connection and try again.',
          model: 'system',
        }

        window.electronAPI.db.addMessage(currentConversationId, errorMessage)
          .then(() => get().loadMessages(currentConversationId))
          .catch(console.error)
      }, 60000) // 60 second timeout

      // Start streaming
      await openRouterService.createStreamingChatCompletion(
        {
          model: settings.selectedModel || 'openai/gpt-3.5-turbo',
          messages: conversationMessages,
          temperature: settings.temperature,
          max_tokens: settings.maxTokens,
          top_p: settings.topP,
          top_k: settings.topK,
          frequency_penalty: settings.frequencyPenalty,
          presence_penalty: settings.presencePenalty,
        },
        // onChunk
        (chunk: string) => {
          streamingContent += chunk
          set({ streamingMessage: streamingContent })
        },
        // onComplete
        async () => {
          clearTimeout(timeoutId)

          // Save the complete message to database
          const assistantMessage: Omit<Message, 'id' | 'created_at'> = {
            conversation_id: currentConversationId,
            role: 'assistant',
            content: streamingContent,
            model: settings.selectedModel,
          }

          await window.electronAPI.db.addMessage(currentConversationId, assistantMessage)
          await get().loadMessages(currentConversationId)
          set({ streamingMessage: null })
        },
        // onError
        async (error: Error) => {
          clearTimeout(timeoutId)
          console.error('Streaming error:', error)

          const errorMessage: Omit<Message, 'id' | 'created_at'> = {
            conversation_id: currentConversationId,
            role: 'assistant',
            content: `Error: ${error.message}`,
            model: 'system',
          }

          await window.electronAPI.db.addMessage(currentConversationId, errorMessage)
          await get().loadMessages(currentConversationId)
          set({ streamingMessage: null })
        }
      )

    } catch (error) {
      console.error('Failed to send streaming message:', error)
      set({ streamingMessage: null })

      // Add error message to chat
      const errorMessage: Omit<Message, 'id' | 'created_at'> = {
        conversation_id: currentConversationId,
        role: 'assistant',
        content: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
        model: 'system',
      }

      try {
        await window.electronAPI.db.addMessage(currentConversationId, errorMessage)
        await get().loadMessages(currentConversationId)
      } catch (dbError) {
        console.error('Failed to save error message:', dbError)
      }
    } finally {
      set({ isLoading: false })
    }
  },

  loadModels: async () => {
    try {
      const { settings } = get()
      if (!settings.openRouterApiKey) {
        console.log('No API key available for loading models')
        return
      }

      openRouterService.setApiKey(settings.openRouterApiKey)
      const models = await openRouterService.getModels()
      set({ models })
      console.log(`Loaded ${models.length} models`)
    } catch (error) {
      console.error('Failed to load models:', error)
    }
  },
}))

// Load initial data
export const initializeApp = async () => {
  // Wait for electron API to be available
  if (typeof window !== 'undefined' && window.electronAPI) {
    const store = useAppStore.getState()
    await store.loadConversations()

    // Load settings
    try {
      const savedSettings = await window.electronAPI.settings.get('app-settings')
      if (savedSettings) {
        store.updateSettings(savedSettings)

        // Auto-load models if API key exists
        if (savedSettings.openRouterApiKey) {
          console.log('Auto-loading models with saved API key...')
          await store.loadModels()
        }
      }
    } catch (error) {
      console.error('Failed to load settings:', error)
    }
  } else {
    console.log('Running in browser mode - electron API not available')
  }
}
