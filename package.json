{"name": "chatlo", "version": "1.0.0", "description": "AI Chat App with OpenRouter Integration", "main": "dist/main.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:vite\" \"wait-on http://localhost:5173 && npm run dev:electron\"", "dev:vite": "vite", "dev:electron": "cross-env NODE_ENV=development electron .", "build": "tsc -p electron && vite build", "build:electron": "tsc -p electron", "build:package": "electron-builder", "build:publish": "npm run build && electron-builder --publish=always", "build:draft": "npm run build && electron-builder --publish=never", "rebuild": "npx electron-rebuild", "postinstall": "electron-builder install-app-deps", "preview": "vite preview"}, "keywords": ["electron", "react", "typescript", "ai", "chat", "openrouter"], "author": "chatlo", "license": "MIT", "build": {"appId": "com.chatlo.app", "productName": "chatlo", "directories": {"output": "release"}, "files": ["dist/**/*", "!dist/**/*.map", "!node_modules/**/*"], "asarUnpack": ["node_modules/better-sqlite3/**/*"], "compression": "maximum", "publish": {"provider": "github", "owner": "chatlo", "repo": "chatlo"}, "mac": {"category": "public.app-category.productivity", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}, "extraResources": [], "extraFiles": []}, "dependencies": {"better-sqlite3": "^12.1.1", "electron-updater": "^6.6.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "uuid": "^11.1.0", "zustand": "^5.0.5"}, "devDependencies": {"@types/better-sqlite3": "^7.6.13", "@types/node": "^24.0.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "electron": "^37.1.0", "electron-builder": "^26.0.12", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "terser": "^5.43.1", "typescript": "^5.8.3", "vite": "^7.0.0", "wait-on": "^8.0.3"}}