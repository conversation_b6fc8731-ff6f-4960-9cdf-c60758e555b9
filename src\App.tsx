import { useAppStore } from './store'
import Sidebar from './components/Sidebar'
import ChatArea from './components/ChatArea'
import { Menu } from './components/Icons'

function App() {
  const { sidebarOpen, setSidebarOpen } = useAppStore()

  return (
    <div className="h-screen flex bg-neutral-950 text-neutral-100 font-sans antialiased selection:bg-indigo-500/60 overflow-hidden">
      {/* Mobile menu button */}
      <div className="md:hidden fixed top-4 left-4 z-50">
        <button
          onClick={() => setSidebarOpen(!sidebarOpen)}
          className="h-8 w-8 flex items-center justify-center rounded hover:bg-neutral-800 bg-neutral-900/80 backdrop-blur-sm"
        >
          <Menu className="h-4 w-4" />
        </button>
      </div>

      {/* Sidebar */}
      <div className={`
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        md:translate-x-0 transition-transform duration-300 ease-in-out
        fixed md:relative z-40 h-full
      `}>
        <Sidebar />
      </div>

      {/* Overlay for mobile */}
      {sidebarOpen && (
        <div 
          className="md:hidden fixed inset-0 bg-black/50 z-30"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Main chat area */}
      <div className="flex-1 flex flex-col min-w-0 h-full">
        <ChatArea />
      </div>
    </div>
  )
}

export default App
