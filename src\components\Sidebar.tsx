import React, { useState, useEffect } from 'react'
import { useAppStore } from '../store'
import { Plus, Settings as SettingsIcon, MessageSquare, Trash2, Edit3, CheckCircle, AlertCircle } from './Icons'
import Settings from './Settings'

// Toast notification component
const Toast: React.FC<{ message: string; type: 'success' | 'error'; onClose: () => void }> = ({ message, type, onClose }) => {
  useEffect(() => {
    const timer = setTimeout(onClose, 4000)
    return () => clearTimeout(timer)
  }, [onClose])

  return (
    <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg backdrop-blur-lg border ${
      type === 'success'
        ? 'bg-green-900/80 border-green-700 text-green-100'
        : 'bg-red-900/80 border-red-700 text-red-100'
    }`}>
      <div className="flex items-center gap-2">
        {type === 'success' ? (
          <CheckCircle className="h-5 w-5" />
        ) : (
          <AlertCircle className="h-5 w-5" />
        )}
        <span className="text-sm font-medium">{message}</span>
        <button
          onClick={onClose}
          className="ml-2 text-current hover:opacity-70"
        >
          ×
        </button>
      </div>
    </div>
  )
}

const Sidebar: React.FC = () => {
  const { 
    conversations, 
    currentConversationId, 
    setCurrentConversation, 
    createConversation, 
    deleteConversation,
    loadMessages 
  } = useAppStore()
  
  const [editingId, setEditingId] = useState<string | null>(null)
  const [editTitle, setEditTitle] = useState('')
  const [showSettings, setShowSettings] = useState(false)
  const [updateStatus, setUpdateStatus] = useState<'idle' | 'checking' | 'available' | 'downloading' | 'downloaded' | 'error'>('idle')
  const [updateError, setUpdateError] = useState<string | null>(null)
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' } | null>(null)

  const handleNewConversation = async () => {
    try {
      console.log('Creating new conversation...')
      const id = await createConversation('New Conversation')
      console.log('Created conversation with ID:', id)
      setCurrentConversation(id)
      await loadMessages(id)
      console.log('Conversation setup complete')
    } catch (error) {
      console.error('Failed to create conversation:', error)
      alert('Failed to create conversation: ' + (error instanceof Error ? error.message : 'Unknown error'))
    }
  }

  const handleSelectConversation = async (id: string) => {
    setCurrentConversation(id)
    await loadMessages(id)
  }

  const handleDeleteConversation = async (id: string, e: React.MouseEvent) => {
    e.stopPropagation()
    if (confirm('Are you sure you want to delete this conversation?')) {
      await deleteConversation(id)
    }
  }

  const handleEditStart = (id: string, title: string, e: React.MouseEvent) => {
    e.stopPropagation()
    setEditingId(id)
    setEditTitle(title)
  }

  const handleEditSave = async (id: string) => {
    if (editTitle.trim()) {
      try {
        await window.electronAPI.db.updateConversation(id, editTitle.trim())
        useAppStore.getState().loadConversations()
      } catch (error) {
        console.error('Failed to update conversation:', error)
      }
    }
    setEditingId(null)
    setEditTitle('')
  }

  const handleEditCancel = () => {
    setEditingId(null)
    setEditTitle('')
  }

  const handleCheckForUpdates = async () => {
    if (!window.electronAPI?.updater) {
      console.log('Updater not available')
      return
    }

    setUpdateStatus('checking')
    setUpdateError(null)

    try {
      const result = await window.electronAPI.updater.checkForUpdates()
      if (result.available) {
        setUpdateStatus('available')
      } else {
        setUpdateStatus('idle')
        if (result.error) {
          setUpdateError(result.error)
          setUpdateStatus('error')
        }
      }
    } catch (error) {
      console.error('Error checking for updates:', error)
      setUpdateError(error instanceof Error ? error.message : 'Unknown error')
      setUpdateStatus('error')
    }
  }

  const handleDownloadAndInstall = async () => {
    if (!window.electronAPI?.updater) {
      return
    }

    setUpdateStatus('downloading')

    try {
      const result = await window.electronAPI.updater.downloadAndInstall()
      if (result.success) {
        setUpdateStatus('downloaded')
        setToast({ message: 'Update downloaded successfully! Restart to apply.', type: 'success' })
      } else {
        setUpdateError(result.error || 'Failed to download update')
        setUpdateStatus('error')
        setToast({ message: result.error || 'Failed to download update', type: 'error' })
      }
    } catch (error) {
      console.error('Error downloading update:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      setUpdateError(errorMessage)
      setUpdateStatus('error')
      setToast({ message: errorMessage, type: 'error' })
    }
  }

  // Set up update event listeners
  useEffect(() => {
    if (!window.electronAPI?.updater) {
      return
    }

    const cleanup: (() => void)[] = []

    // Set up event listeners
    window.electronAPI.updater.onCheckingForUpdate(() => {
      setUpdateStatus('checking')
    })

    window.electronAPI.updater.onUpdateAvailable((info) => {
      console.log('Update available:', info)
      setUpdateStatus('available')
    })

    window.electronAPI.updater.onUpdateNotAvailable(() => {
      setUpdateStatus('idle')
    })

    window.electronAPI.updater.onError((error) => {
      console.error('Update error:', error)
      setUpdateError(error)
      setUpdateStatus('error')
      setToast({ message: error, type: 'error' })
    })

    window.electronAPI.updater.onDownloadProgress((progress) => {
      console.log('Download progress:', progress.percent + '%')
      setUpdateStatus('downloading')
    })

    window.electronAPI.updater.onUpdateDownloaded((info) => {
      console.log('Update downloaded:', info)
      setUpdateStatus('downloaded')
      setToast({ message: 'Update downloaded successfully! Restart to apply.', type: 'success' })
    })

    return () => {
      cleanup.forEach(fn => fn())
    }
  }, [])

  return (
    <aside className="flex flex-col w-64 bg-neutral-900/60 backdrop-blur-lg border-r border-neutral-800 h-screen md:w-64">
      {/* Header */}
      <header className="flex items-center gap-2 h-16 px-6 border-b border-neutral-800">
        <div className="flex items-center justify-center">
          <img src="/chatlo_logo_dark.svg" alt="ChatLo" className="h-11 w-auto" />
        </div>
      </header>

      {/* New Conversation Button */}
      <div className="p-4">
        <button
          onClick={handleNewConversation}
          className="w-full flex items-center gap-2 px-4 py-3 bg-indigo-500 hover:bg-indigo-600 rounded-lg text-sm font-medium transition-colors"
        >
          <Plus className="h-4 w-4" />
          New Conversation
        </button>
      </div>

      {/* Conversations List */}
      <nav className="flex-1 overflow-y-auto px-2">
        <div className="space-y-1">
          {conversations.map((conversation) => (
            <div
              key={conversation.id}
              className={`
                group relative flex items-center gap-3 px-4 py-3 rounded-lg text-sm font-medium cursor-pointer transition-colors
                ${currentConversationId === conversation.id 
                  ? 'bg-neutral-800 text-white' 
                  : 'hover:bg-neutral-800/50 text-neutral-300'
                }
              `}
              onClick={() => handleSelectConversation(conversation.id)}
            >
              <MessageSquare className="h-4 w-4 shrink-0" />
              
              {editingId === conversation.id ? (
                <input
                  type="text"
                  value={editTitle}
                  onChange={(e) => setEditTitle(e.target.value)}
                  onBlur={() => handleEditSave(conversation.id)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') handleEditSave(conversation.id)
                    if (e.key === 'Escape') handleEditCancel()
                  }}
                  className="flex-1 bg-transparent border-none outline-none text-sm"
                  autoFocus
                  onClick={(e) => e.stopPropagation()}
                />
              ) : (
                <span className="flex-1 truncate">{conversation.title}</span>
              )}

              {/* Action buttons */}
              <div className="opacity-0 group-hover:opacity-100 flex items-center gap-1 transition-opacity">
                <button
                  onClick={(e) => handleEditStart(conversation.id, conversation.title, e)}
                  className="p-1 hover:bg-neutral-700 rounded"
                >
                  <Edit3 className="h-3 w-3" />
                </button>
                <button
                  onClick={(e) => handleDeleteConversation(conversation.id, e)}
                  className="p-1 hover:bg-neutral-700 rounded text-red-400"
                >
                  <Trash2 className="h-3 w-3" />
                </button>
              </div>
            </div>
          ))}
        </div>
      </nav>

      {/* Settings */}
      <footer className="h-16 border-t border-neutral-800 px-6 flex items-center gap-3">
        <div className="flex-1">
          <p className="text-sm font-medium leading-none">chatlo</p>
          <p className="text-xs text-neutral-400">v1.0.0</p>
        </div>

        {/* Update Button */}
        <div className="flex items-center gap-2">
          {updateStatus === 'available' && (
            <span className="text-xs text-red-400 animate-pulse font-medium">
              Update Available
            </span>
          )}
          <button
            onClick={updateStatus === 'available' ? handleDownloadAndInstall : handleCheckForUpdates}
            disabled={updateStatus === 'checking' || updateStatus === 'downloading'}
            className={`h-8 w-8 rounded hover:bg-neutral-800 flex items-center justify-center transition-colors ${
              updateStatus === 'available' ? 'text-red-400 hover:text-red-300' :
              updateStatus === 'error' ? 'text-red-400 hover:text-red-300' :
              updateStatus === 'downloaded' ? 'text-blue-400 hover:text-blue-300' :
              'text-neutral-400 hover:text-neutral-300'
            }`}
            title={
              updateStatus === 'checking' ? 'Checking for updates...' :
              updateStatus === 'available' ? 'Update available - Click to download' :
              updateStatus === 'downloading' ? 'Downloading update...' :
              updateStatus === 'downloaded' ? 'Update ready - Restart to apply' :
              updateStatus === 'error' ? `Update error: ${updateError}` :
              'Check for updates'
            }
          >
            {updateStatus === 'checking' ? (
              <img src="/update-arrows-svgrepo-com.svg" className="h-4 w-4 animate-spin" alt="Checking" />
            ) : updateStatus === 'downloading' ? (
              <img src="/update-arrows-svgrepo-com.svg" className="h-4 w-4 animate-spin" alt="Downloading" />
            ) : updateStatus === 'available' ? (
              <img src="/update-arrows-svgrepo-com.svg" className="h-4 w-4" alt="Update Available" />
            ) : updateStatus === 'downloaded' ? (
              <CheckCircle className="h-4 w-4" />
            ) : updateStatus === 'error' ? (
              <AlertCircle className="h-4 w-4" />
            ) : (
              <img src="/update-arrows-svgrepo-com.svg" className="h-4 w-4" alt="Update" />
            )}
          </button>
        </div>

        <button
          onClick={() => setShowSettings(true)}
          className="h-8 w-8 rounded hover:bg-neutral-800 flex items-center justify-center"
        >
          <SettingsIcon className="h-4 w-4" />
        </button>
      </footer>

      {/* Settings Modal */}
      <Settings
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />

      {/* Toast Notifications */}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </aside>
  )
}

export default Sidebar



